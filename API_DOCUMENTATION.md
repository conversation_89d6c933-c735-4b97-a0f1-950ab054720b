# 小美人际关系管理系统 API 文档

## 概述

小美人际关系管理系统提供了一套完整的 API 接口，用于管理人际关系、记忆存储和智能对话功能。支持流式和非流式响应。

**测试环境地址**: `http://xiaomeiai.cloud.test.sankuai.com`
**生产环境地址**: `https://xiaomeiai.meituan.com/humanrelation`

## 目录

1. [记忆管理接口](#记忆管理接口)
   - 搜索记忆
   - 添加记忆
   - 读取记忆
   - 更新长期记忆
   - 检查过期信息
   - 根据人员 ID 搜索记忆
   - 获取用户所有短期记忆
   - 获取用户所有人员 ID
   - 更新事件内容
   - 添加人员事件
   - 删除人员事件
2. [对话聊天接口](#对话聊天接口)
   - 流式聊天
   - JSON 格式聊天
3. [人员管理接口](#人员管理接口)
   - 获取所有人员列表
   - 获取单个人员信息
   - 添加人员
   - 更新人员信息
   - 删除人员
   - 搜索人员
   - 合并人员档案
   - 获取用户本人档案
4. [会话管理接口](#会话管理接口)
   - 生成新的会话 ID
   - 获取用户的所有会话 ID
   - 获取聊天记录
5. [提醒管理接口](#提醒管理接口)
   - 添加提醒
   - 删除提醒
   - 获取用户提醒列表
   - 检查提醒线程状态
   - 重启提醒线程
6. [天气查询接口](#天气查询接口)
7. [天气相关信息接口](#天气相关信息接口)
8. [话题推荐接口](#话题推荐接口)
   - 根据用户档案和事件推荐话题
9. [系统监控接口](#系统监控接口)

---

## 记忆管理接口

### 1. 搜索记忆

**接口**: `POST /humanrelation/search_memory`

**描述**: 根据语义相关性搜索用户的记忆内容

**请求参数**:

```json
{
  "user_input": "string", // 用户输入内容，根据语义相关性查询
  "size": "integer", // 返回结果数量
  "k": "integer", // 候选数量
  "user_id": "string", // 查询者的用户 mis id
  "memory_type": "string" // 记忆类型，分long or short
}
```

**响应示例**:

```json
{
  "results": [
    {
      "content": "记忆内容",
      "score": 0.95,
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 添加记忆

**接口**: `POST /humanrelation/add_memory`

**描述**: 从对话文本中提取并存储人员和事件记忆

**请求参数**:

```json
{
  "conversation_text": "string", // 对话文本
  "user_id": "string" // 用户ID
}
```

**响应示例**:

```json
{
  "status": "success",
  "message": "记忆添加成功",
  "extracted_entities": ["人员1", "事件1"]
}
```

### 3. 读取记忆

**接口**: `POST /humanrelation/read_memory`

**描述**: 根据查询文本检索相关的人员和事件记忆

**请求参数**:

```json
{
  "query_text": "string", // 查询文本
  "user_id": "string", // 用户ID
  "max_results": "integer" // 最大结果数，默认10
}
```

### 4. 更新长期记忆

**接口**: `POST /humanrelation/update_long_memory`

**描述**: 手动触发长期记忆更新，将短期记忆总结为人物小传

**请求参数**:

- `user_id` (query parameter): 用户 ID

### 5. 检查过期信息

**接口**: `GET /humanrelation/check_outdated`

**描述**: 检查需要更新的过期状态信息

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `days` (query parameter): 过期天数，默认 180 天

### 6. 根据人员 ID 搜索记忆

**接口**: `POST /humanrelation/search_memory_by_person_id`

**描述**: 根据人员 ID 搜索相关的事件记忆

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "person_id": "string" // 人员ID
}
```

**响应示例**:

```json
{
  "result": "success",
  "events": [
    {
      "event_id": "uuid-here",
      "user_id": "user123",
      "description_text": "和张三讨论项目进展",
      "timestamp": "2024-01-01T10:00:00Z",
      "participants": ["person_id_123"],
      "location": "会议室A",
      "topics": ["项目", "进展"],
      "sentiment": "积极"
    }
  ]
}
```

### 7. 获取用户所有短期记忆

**接口**: `GET /humanrelation/get_all_short_memory_by_user_id`

**描述**: 根据用户 ID 获取对应的所有短期记忆（事件记忆）

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `size` (query parameter): 返回数量，默认 100

**响应示例**:

```json
{
  "result": "success",
  "events": [
    {
      "event_id": "uuid-here",
      "user_id": "user123",
      "description_text": "和李四在咖啡厅讨论新项目",
      "timestamp": "2024-01-01T15:30:00Z",
      "participants": ["person_id_456"],
      "location": "星巴克",
      "topics": ["项目", "讨论"],
      "sentiment": "积极"
    }
  ]
}
```

### 8. 获取用户所有人员 ID

**接口**: `GET /humanrelation/get_all_person_id_by_user_id`

**描述**: 根据用户 ID 获取所有相关人员的 ID 列表

**请求参数**:

- `user_id` (query parameter): 用户 ID

**响应示例**:

```json
{
  "result": "success",
  "person_ids": ["person_id_123", "person_id_456", "person_id_789"],
  "count": 3
}
```

### 9. 更新事件内容

**接口**: `PUT /humanrelation/update_event`

**描述**: 更新事件内容，支持修正 AI 编辑错误和关联错误

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "event_id": "string", // 事件ID
  "description_text": "string", // 事件描述（可选）
  "participants": ["person_id_1", "person_id_2"], // 参与者列表（可选）
  "location": "string", // 地点（可选）
  "topics": ["主题1", "主题2"], // 主题列表（可选）
  "sentiment": "string" // 情感（可选）
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "事件更新成功",
  "event_id": "event_uuid_here",
  "updated_fields": ["description_text", "participants"]
}
```

**AI 自动情感分析**:

当请求中未提供 `sentiment` 字段或该字段为空时，系统会自动调用 AI 分析事件描述的情感倾向：

- **positive（积极）**: 表达正面情绪的事件，如成功、快乐、庆祝、成就、友好交流等
- **negative（消极）**: 表达负面情绪的事件，如失败、悲伤、冲突、问题、不愉快等
- **neutral（中性）**: 客观描述的事件，没有明显的情感色彩，如日常活动、会议、信息记录等

**使用场景**:

- 手动添加重要事件记忆
- 补充 AI 自动提取遗漏的事件信息
- 记录特定时间点的重要事件

### 10. 添加人员事件

**接口**: `POST /humanrelation/add_person_event`

**描述**: 手动添加指定人员的事件记忆，支持 AI 自动情感分析

**请求参数**:

```json
{
  "user_id": "string", // 用户ID（必填）
  "description_text": "string", // 事件描述（可选）
  "participants": ["person_name_1", "person_name_2"], // 参与者列表（可选）
  "location": "string", // 地点（可选）
  "topics": ["主题1", "主题2"], // 主题列表（可选）
  "sentiment": "string" // 情感（可选，如不提供将AI自动分析）
}
```

**响应示例**:

```json
{
  "result": "success",
  "event_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "user_id不能为空"
}
```

**使用场景**:

- 手动补充 AI 未能自动提取的重要事件
- 添加历史事件记录
- 批量导入事件数据

### 11. 删除人员事件

**接口**: `DELETE /humanrelation/delete_person_event`

**描述**: 删除指定的人员事件记忆

**请求参数**:

```json
{
  "user_id": "string", // 用户ID（必填）
  "event_id": "string" // 事件ID（必填）
}
```

**响应示例**:

```json
{
  "result": "success",
  "event_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "事件不存在或无权限删除"
}
```

**安全特性**:

- 用户只能删除自己的事件
- 删除前会验证事件是否存在且属于该用户
- 详细的操作日志记录

**使用场景**:

- 删除错误添加的事件
- 清理过时的事件记录
- 数据维护和管理

---

## 对话聊天接口

### 1. 流式聊天

**接口**: `POST /humanrelation/chat`

**描述**: 流式聊天接口，使用 conversation_id 维护上下文

**请求参数**:

```json
{
  "content": "string", // 用户问题
  "conversation_id": "string", // 对话 ID，用于上下文关联
  "user_id": "string" // 用户ID
}
```

**响应**: Server-Sent Events (SSE) 流式响应

```
Content-Type: text/event-stream

data: {"content": "累积的回复内容", "type": "chat", "is_final": false}
data: {"content": "更多累积的回复内容", "type": "chat", "is_final": false}
data: {"tool_name": "工具名称", "tool_input": {...}, "status": "start", "is_final": false}
data: {"tool_name": "工具名称", "tool_output": "工具输出", "status": "end", "is_final": false}
data: {"content": "完整的AI回复内容", "type": "chat", "is_final": true}
data: {"type": "recommendations", "questions": ["推荐问题1？", "推荐问题2？", "推荐问题3？"]}
```

**响应类型说明**:

- `type: "chat"`: AI 回答内容，`content`字段包含累积的回答文本
- `tool_name + status: "start"`: 工具调用开始，包含工具名称和输入参数
- `tool_name + status: "end"`: 工具调用结束，包含工具输出结果
- `type: "recommendations"`: 推荐问题，在 AI 回答完成后发送，包含推荐问题数组
- `is_final`: 布尔字段，仅在 `type: "chat"` 消息中出现，标识 AI 回答内容是否完成
  - `false`: AI 回答内容未完成，后续还有更多内容
  - `true`: AI 回答内容已完成，这是最后一条回答消息

### 2. JSON 格式聊天

**接口**: `POST /humanrelation/chat_json`

**描述**: 非流式聊天接口，返回 JSON 格式，包含推荐问题

**请求参数**:

```json
{
  "content": "string", // 用户问题
  "conversation_id": "string", // 对话 ID，用于上下文关联
  "user_id": "string" // 用户ID
}
```

**响应示例**:

```json
{
  "response": "AI回复内容",
  "status": "success",
  "recommended_questions": ["推荐问题1？", "推荐问题2？", "推荐问题3？"]
}
```

**功能特点**:

- **推荐问题生成**: 基于当前对话上下文自动生成相关的推荐问题
- **配置化控制**: 通过 Lion 配置中心的 `humanrelation.enable_recommendations` 参数控制是否启用推荐问题功能
- **容错处理**: 推荐问题生成失败不影响主要聊天功能，会返回空数组
- **智能上下文**: 推荐问题基于用户输入和 AI 回复内容生成，更贴合当前对话场景
- **双向关系处理**: 系统在处理人际关系信息时会自动建立双向关联，确保关系完整性
- **时效性处理**: 支持年龄信息自动转换为出生年份，保持数据的时效性
- **智能数据标准化**: 自动将 AI 解析结果转换为标准数据结构，保留有价值信息

---

## 人员管理接口

### 1. 获取所有人员列表

**接口**: `GET /humanrelation/persons`

**描述**: 获取所有人员列表

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `limit` (query parameter): 返回数量，默认 100
- `offset` (query parameter): 偏移量，默认 0

**响应示例**:

```json
{
  "result": "success",
  "persons": [
    {
      "person_id": "7611346a-01d0-4323-934a-fa6989156db2",
      "user_id": "zhangzheng51",
      "is_user": false,
      "canonical_name": "张三",
      "aliases": "三三",
      "relationships": [
        {
          "type": "朋友",
          "target": "USER"
        }
      ],
      "profile_summary": "张三，亦称"三三"，是产品技术领域的资深专家，以技术方案制定、架构设计及性能优化的深厚功力著称。他具备敏锐的技术洞察力与创新思维，能够为团队提供高价值的专业建议。",
      "key_attributes": {
        "关系": "",
        "年龄": "",
        "职业": "",
        "兴趣": "",
        "期望": "",
        "关心话题": "",
        "基本信息": {
          "家乡": "",
          "性别": "",
          "生日": "",
          "家庭情况": {
            "婚育状况": "",
            "子女信息": [],
            "配偶姓名": ""
          },
          "当前城市": "",
          "职业信息": {
            "公司": "",
            "职位": "",
            "行业": ""
          },
          "联系方式": {
            "电话": "",
            "邮箱": "",
            "社交账号": {
              "微信": ""
            }
          }
        },
        "旅游历史": "",
        "过往历史": "关系: 朋友",
        "餐饮偏好": ""
      },
      "avatar": "",
      "created_at": "2025-07-10T19:11:37",
      "updated_at": "2025-07-17T16:42:06"
    }
  ]
}
```

### 2. 获取单个人员信息

**接口**: `GET /humanrelation/person/{person_id}`

**描述**: 根据 ID 获取人员详细信息

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `person_id` (path parameter): 人员 ID

**响应示例**:

```json
{
  "result": "success",
  "person": {
    "person_id": "7611346a-01d0-4323-934a-fa6989156db2",
    "user_id": "zhangzheng51",
    "is_user": false,
    "canonical_name": "张三",
    "aliases": "三三",
    "relationships": [
      {
        "type": "朋友",
        "target": "USER"
      }
    ],
    "profile_summary": "张三，亦称"三三"，是产品技术领域的资深专家，以技术方案制定、架构设计及性能优化的深厚功力著称。",
    "key_attributes": {
      "关系": "",
      "年龄": "",
      "职业": "",
      "兴趣": "",
      "期望": "",
      "关心话题": "",
      "基本信息": {
        "家乡": "",
        "性别": "",
        "生日": "",
        "家庭情况": {
          "婚育状况": "",
          "子女信息": [],
          "配偶姓名": ""
        },
        "当前城市": "",
        "职业信息": {
          "公司": "",
          "职位": "",
          "行业": ""
        },
        "联系方式": {
          "电话": "",
          "邮箱": "",
          "社交账号": {
            "微信": ""
          }
        }
      },
      "旅游历史": "",
      "过往历史": "关系: 朋友",
      "餐饮偏好": ""
    },
    "avatar": "",
    "created_at": "2025-07-10T19:11:37",
    "updated_at": "2025-07-17T16:42:06"
  }
}
```

### 3. 添加人员

**接口**: `POST /humanrelation/add_person`

**描述**: 添加新人员

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "canonical_name": "string", // 正式姓名
  "aliases": "string", // 别名，默认空字符串
  "relationships": [
    {
      "type": "string", // 关系类型，如"朋友"、"同事"等
      "target": "string" // 关系目标，如"USER"或其他人员姓名
    }
  ], // 人际关系数组，默认空数组
  "profile_summary": "string", // 个人简介，默认空字符串
  "key_attributes": {
    "关系": "string",
    "年龄": "string",
    "职业": "string",
    "兴趣": "string",
    "期望": "string",
    "关心话题": "string",
    "基本信息": {
      "家乡": "string",
      "性别": "string",
      "生日": "string",
      "家庭情况": {
        "婚育状况": "string",
        "子女信息": [], // 子女姓名数组
        "配偶姓名": "string"
      },
      "当前城市": "string",
      "职业信息": {
        "公司": "string",
        "职位": "string",
        "行业": "string"
      },
      "联系方式": {
        "电话": "string",
        "邮箱": "string",
        "社交账号": {
          "微信": "string"
        }
      }
    },
    "旅游历史": "string",
    "过往历史": "string",
    "餐饮偏好": "string"
  }, // 关键属性，采用标准化结构
  "avatar": "string", // 头像URL，默认空字符串
  "is_user": false // 是否为用户，默认false
}
```

**注意**: 系统会自动生成 `person_id`，无需在请求中提供。如果需要指定特定的 `person_id`，可以通过后端服务层的 `person_id` 参数传入。

**响应示例**:

```json
{
  "result": "success",
  "person_id": "generated-uuid-here"
}
```

### 4. 更新人员信息

**接口**: `PUT /humanrelation/change_person/{person_id}`

**描述**: 更新人员档案信息

**请求参数**:

- `person_id` (path parameter): 人员 ID
- 请求体同添加人员接口

**响应示例**:

```json
{
  "result": "success"
}
```

### 5. 删除人员

**接口**: `DELETE /humanrelation/person/{person_id}`

**描述**: 删除指定人员

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `person_id` (path parameter): 人员 ID

**响应示例**:

```json
{
  "result": "success"
}
```

### 6. 搜索人员

**接口**: `GET /humanrelation/search_person`

**描述**: 按姓名搜索人员

**请求参数**:

- `user_id` (query parameter): 用户 ID
- `name` (query parameter): 搜索的姓名
- `limit` (query parameter): 结果限制数量，默认 10

**响应示例**:

```json
{
  "result": "success",
  "persons": [
    {
      "person_id": "7611346a-01d0-4323-934a-fa6989156db2",
      "user_id": "zhangzheng51",
      "is_user": false,
      "canonical_name": "张三",
      "aliases": "三三",
      "relationships": [
        {
          "type": "朋友",
          "target": "USER"
        }
      ],
      "profile_summary": "张三，亦称"三三"，是产品技术领域的资深专家",
      "key_attributes": {
        "基本信息": {
          "家庭情况": {
            "子女信息": [],
            "配偶姓名": ""
          },
          "职业信息": {
            "公司": "",
            "职位": "",
            "行业": ""
          }
        },
        "过往历史": "关系: 朋友"
      },
      "avatar": "",
      "created_at": "2025-07-10T19:11:37",
      "updated_at": "2025-07-17T16:42:06"
    }
  ]
}
```

### 7. 合并人员档案

**接口**: `POST /humanrelation/merge_persons`

**描述**: 确认并执行两个人员档案的合并

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "primary_person_id": "string", // 主要人员ID
  "secondary_person_id": "string" // 次要人员ID
}
```

**响应示例**:

```json
{
  "result": "success",
  "message": "人员档案合并成功"
}
```

### 8. 获取用户本人档案

**接口**: `GET /humanrelation/get_user_profile`

**描述**: 获取用户本人的档案信息

**请求参数**:

- `user_id` (query parameter): 用户 ID

**响应示例**:

```json
{
  "result": "success",
  "person": {
    "person_id": "uuid-here",
    "user_id": "user123",
    "canonical_name": "张三",
    "aliases": "小张",
    "relationships": ["用户本人"],
    "profile_summary": "用户本人档案",
    "key_attributes": {
      "城市": "北京",
      "职业": "工程师",
      "工作地点": "朝阳区",
      "居住地": "海淀区"
    },
    "avatar": "",
    "is_user": true
  }
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "用户本人档案不存在"
}
```

**功能特点**:

- **自动创建**: 系统会在用户首次使用时自动创建用户本人档案
- **特殊标识**: 用户本人档案的 `is_user` 字段为 `true`
- **地点信息**: 支持在 `key_attributes` 中存储多个地点信息，用于天气查询等功能
- **双向关系处理**: 系统自动处理家庭关系的双向关联，确保关系信息的完整性

### 9. 双向关系处理机制

**功能描述**: 系统在处理人际关系时会自动建立双向关联，确保关系信息在相关人员档案中都有体现。

**支持的关系类型**:

- **父女/母子关系**: 当添加"李四有个女儿叫李佳佳"时，系统会：

  - 在李佳佳档案中添加：`{"type": "父亲", "target": "李四"}`
  - 在李四档案中自动添加：`{"type": "女儿", "target": "李佳佳"}`
  - 在李四的 `key_attributes.基本信息.家庭情况.子女信息` 中添加：`["李佳佳"]`

- **夫妻/配偶关系**: 当添加配偶关系时，系统会：
  - 在双方档案中都添加相应的配偶关系
  - 在 `key_attributes.基本信息.家庭情况.配偶姓名` 中更新配偶信息

**关系映射表**:

| 原始关系 | 反向关系 | 更新字段           |
| -------- | -------- | ------------------ |
| 父亲     | 女儿     | 父亲档案的子女信息 |
| 母亲     | 女儿     | 母亲档案的子女信息 |
| 儿子     | 父亲     | 父亲档案的子女信息 |
| 女儿     | 父亲     | 父亲档案的子女信息 |
| 丈夫     | 妻子     | 双方档案的配偶姓名 |
| 妻子     | 丈夫     | 双方档案的配偶姓名 |
| 配偶     | 配偶     | 双方档案的配偶姓名 |

**处理流程**:

1. **AI 解析**: 从用户输入中识别人物关系
2. **创建/更新人物档案**: 处理主要人物信息
3. **双向关系处理**: 自动为相关人物添加反向关系
4. **属性字段更新**: 更新相关的家庭情况字段

**示例场景**:

```json
// 用户输入: "我朋友张三有个5岁的儿子叫小明"
// 系统处理结果:

// 张三的档案
{
  "canonical_name": "张三",
  "relationships": [
    {"type": "朋友", "target": "USER"},
    {"type": "父亲", "target": "小明"}  // 自动添加
  ],
  "key_attributes": {
    "基本信息": {
      "家庭情况": {
        "子女信息": ["小明"]  // 自动添加
      }
    }
  }
}

// 小明的档案
{
  "canonical_name": "小明",
  "relationships": [
    {"type": "儿子", "target": "张三"}  // AI解析
  ],
  "key_attributes": {
    "基本信息": {
      "生日": "2019年"  // 根据年龄自动计算
    }
  }
}
```

**技术特点**:

- **自动触发**: 在人物信息处理完成后自动执行
- **去重处理**: 避免重复添加相同的关系
- **容错机制**: 某个关系处理失败不影响其他关系
- **日志记录**: 详细记录双向关系处理过程，便于调试

---

## 会话管理接口

### 1. 生成新的会话 ID

**接口**: `POST /humanrelation/create_conversation`

**描述**: 为新对话生成一个唯一的、带时间戳的 conversation_id

**请求参数**:

```json
{
  "user_id": "string" // 用户ID
}
```

**响应示例**:

```json
{
  "success": true,
  "conversation_id": "user123_2024-01-01-10-30-45"
}
```

### 2. 获取用户的会话 ID（支持分页）

**接口**: `GET /humanrelation/conversations`

**描述**: 根据用户 ID 获取其历史对话的 conversation_id 列表，包含聊天内容概括作为标题。支持分页查询，优化大量会话的性能表现。

**请求参数**:

- `user_id` (query parameter, 必需): 用户 ID
- `limit` (query parameter, 可选): 每页返回的会话数量，默认 20，最大 100
- `offset` (query parameter, 可选): 偏移量，默认 0

**请求示例**:

```
# 获取第1页（默认每页20条）
GET /humanrelation/conversations?user_id=zhangzheng51

# 获取第1页（每页5条）
GET /humanrelation/conversations?user_id=zhangzheng51&limit=5&offset=0

# 获取第2页（每页5条）
GET /humanrelation/conversations?user_id=zhangzheng51&limit=5&offset=5
```

**响应示例**:

```json
{
  "success": true,
  "user_id": "zhangzheng51",
  "summary": {
    "zhangzheng51_2025-07-16-10-32-48": "对话记录",
    "zhangzheng51_2025-07-16-10-31-55": "张三相关话题讨论",
    "zhangzheng51_2025-07-15-20-32-49": "与王五相关的话题交流"
  },
  "conversations": [
    {
      "conversation_id": "zhangzheng51_2025-07-16-10-32-48",
      "summary": "对话记录"
    },
    {
      "conversation_id": "zhangzheng51_2025-07-16-10-31-55",
      "summary": "张三相关话题讨论"
    },
    {
      "conversation_id": "zhangzheng51_2025-07-15-20-32-49",
      "summary": "与王五相关的话题交流"
    }
  ],
  "pagination": {
    "total_count": 39,
    "current_page": 1,
    "page_size": 5,
    "has_more": true
  },
  "performance": {
    "total_time_seconds": 0.132,
    "db_query_time_seconds": 0.128,
    "conversation_count": 3
  }
}
```

**功能特点**:

- **智能摘要**: 每个会话都包含基于聊天内容生成的智能摘要，便于用户快速识别对话主题
- **异步生成**: 新会话摘要异步生成，首次访问显示"生成中..."，后续访问显示完整摘要
- **分页支持**: 支持分页查询，避免大量数据一次性加载影响性能
- **性能优化**: 使用批量查询和数据库索引，大幅提升查询速度
- **时间排序**: 会话按时间倒序排列，最新的对话在前
- **用户隔离**: 只返回当前用户的会话，确保数据安全

**性能表现**:

- **查询速度**: 分页查询通常在 0.1-0.2 秒内完成
- **数据量控制**: 每页最多 100 条记录，避免大数据包传输
- **扩展性**: 支持用户拥有大量历史会话的场景

### 3. 获取聊天记录

**接口**: `GET /humanrelation/history`

**描述**: 根据对话 ID 获取完整的聊天记录

**请求参数**:

- `conversation_id` (query parameter): 对话 ID
- `user_id` (query parameter): 用户 ID

**响应示例**:

```json
{
  "conversation_id": "conv_123",
  "history": [...],
  "status": "success"
}
```

---

## 提醒管理接口

### 1. 添加提醒

**接口**: `POST /humanrelation/add_reminder`

**描述**: 添加一条提醒到系统中

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "base_event_date": "2024-01-01T00:00:00Z", // 基础事件日期
  "next_trigger_time": "2024-01-01T00:00:00Z", // 下次触发时间
  "subject_person_id": "string", // 相关人员ID（可选）
  "reminder_text_template": "string", // 提醒文本模板（可选）
  "advance_notice_config": {}, // 提前通知配置（可选）
  "recurrence_rule": "string", // 重复规则（可选）
  "status": "active" // 状态，默认为active
}
```

**响应示例**:

```json
{
  "success": true,
  "reminder_id": "12345"
}
```

### 2. 删除提醒

**接口**: `POST /humanrelation/delete_reminder`

**描述**: 删除指定用户的提醒，确保用户隔离

**请求参数**:

```json
{
  "user_id": "string", // 用户ID
  "reminder_id": "integer" // 提醒ID
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "删除成功"
}
```

**错误响应示例**:

```json
{
  "success": false,
  "message": "删除失败或提醒不存在"
}
```

### 3. 获取用户提醒列表

**接口**: `GET /humanrelation/list_reminders`

**描述**: 获取指定用户的所有提醒列表，确保用户隔离

**请求参数**:

- `user_id` (query parameter): 用户 ID

**响应示例**:

```json
{
  "success": true,
  "reminders": [
    {
      "reminder_id": 1,
      "user_id": "user123",
      "subject_person_id": "person_456",
      "reminder_text_template": "记得给张三打电话",
      "base_event_date": "2024-01-01T10:00:00Z",
      "next_trigger_time": "2024-01-01T09:30:00Z",
      "advance_notice_config": { "minutes": 30 },
      "recurrence_rule": "daily",
      "status": "active",
      "created_at": "2024-01-01T08:00:00Z",
      "updated_at": "2024-01-01T08:00:00Z"
    }
  ],
  "count": 1
}
```

### 4. 检查提醒线程状态

**接口**: `GET /humanrelation/reminder_thread_status`

**描述**: 监察接口，检查后台提醒线程状态

**响应示例**:

```json
{
  "is_alive": true,
  "start_time": "2024-01-01 10:00:00",
  "status_message": "线程正常运行"
}
```

### 5. 重启提醒线程

**接口**: `POST /humanrelation/restart_reminder_thread`

**描述**: 重启后台提醒线程

**响应示例**:

```json
{
  "success": true,
  "message": "后台提醒线程重启成功"
}
```

---

## 天气查询接口

### 1. 根据人员地点获取天气

**接口**: `POST /humanrelation/get_weather`

**描述**: 根据指定人员档案中的地点信息获取天气，支持多地点查询（如工作地点和居住地不同）

**请求参数**:

```json
{
  "user_id": "string", // 用户ID（用于权限验证）
  "person_id": "string" // 人员ID（要查询天气的人员档案）
}
```

**功能特点**:

- **智能地点提取**: 从人员档案的 `key_attributes` 和 `profile_summary` 中智能提取地点信息
- **多地点支持**: 如果人员有多个地点（如工作地点、居住地），会返回所有地点的天气
- **权限控制**: 通过 `user_id` 确保用户只能查询自己有权限访问的人员档案
- **容错处理**: 某个地点天气获取失败不影响其他地点
- **体感温度优先**: 优先显示体感温度，提供更准确的温度感受
- **个性化天气提醒**: 基于用户档案（年龄、职业、兴趣等）生成针对性的天气建议
- **AI 智能分析**: 结合天气数据和用户信息，提供穿衣、出行、健康等多维度建议

**地点提取逻辑**:

1. **优先从 key_attributes 提取**: 查找包含地点相关关键词的字段
   - 中文关键词: `地点`、`城市`、`居住地`、`工作地点`、`生活地点`、`常住地` 等
   - 英文关键词: `location`、`city`、`address`、`workplace`、`residence` 等
2. **备选从 profile_summary 提取**: 使用模式匹配识别城市名（如"在北京工作"、"住上海"等）

**成功响应示例**（多地点，包含个性化提醒）:

```json
{
  "result": "success",
  "locations": ["北京", "天津"],
  "weather_data": {
    "工作地点": {
      "city": "北京",
      "weather_info": "当前时间2025-07-18，北京的天气为晴，体感温度为3摄氏度（实际气温5°C），风向为北风，风速为2米/秒，属于2级风，湿度45%",
      "personalized_reminder": "张三，今日北京晴朗但体感较冷，建议穿厚外套保暖。空气干燥注意补水，紫外线较强外出请做好防护。作为工程师建议适当户外活动，有助于缓解工作压力。"
    },
    "居住地": {
      "city": "天津",
      "weather_info": "当前时间2025-07-18，天津的天气为多云，体感温度为1摄氏度（实际气温3°C），风向为西北风，风速为3米/秒，属于3级风，湿度60%",
      "personalized_reminder": "天津今日多云偏冷，体感1°C建议穿羽绒服或厚外套。风力适中，出行注意保暖。湿度适宜，室内环境舒适。"
    }
  }
}
```

**成功响应示例**（单地点）:

```json
{
  "result": "success",
  "locations": ["北京"],
  "weather_data": {
    "从简介提取": {
      "city": "北京",
      "weather_info": "当前时间2025-01-11，北京的天气为晴，气温为5摄氏度，风向为北风，风速为2米/秒，属于2级风"
    }
  }
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "人员档案不存在或无权限访问"
}
```

```json
{
  "result": "error",
  "reason": "用户档案中未找到地点信息",
  "suggestion": "请先在个人档案中设置您的所在城市，可以通过修改档案接口添加'城市'或'居住地'等字段"
}
```

**使用场景**:

- 查询自己的天气信息
- 查询朋友、同事的天气信息
- 为出差、会面等场景提供多地点天气参考

### 2. 获取用户综合天气信息

**接口**: `GET /humanrelation/comprehensive_weather`

**描述**: 获取用户的综合天气信息，包括用户常用地址、即将发生事件地址、直属亲属地址、老板地址的天气，并提供 AI 生成的个性化天气提醒

**请求参数**:

- `user_id` (string, required): 用户 ID

**请求示例**:

```
GET /humanrelation/comprehensive_weather?user_id=zhangzheng51
```

**成功响应示例**:

```json
{
  "result": "success",
  "user_info": {
    "user_id": "zhangzheng51",
    "name": "张三",
    "current_city": "上海",
    "hometown": "北京"
  },
  "weather_summary": {
    "total_locations": 4,
    "locations_with_weather": 3,
    "locations_failed": 1
  },
  "weather_data": {
    "用户常用地址": {
      "上海": {
        "weather_info": "当前时间2025-07-25，上海的天气为多云，温度26°C（体感温度30°C），湿度85%，西南风2级",
        "source": "用户当前城市"
      },
      "北京": {
        "weather_info": "当前时间2025-07-25，北京的天气为阵雨，温度28°C（体感温度33°C），湿度88%，南风1级",
        "source": "用户家乡"
      }
    },
    "即将发生事件地址": {
      "上海": {
        "weather_info": "当前时间2025-07-25，上海的天气为多云，温度26°C（体感温度30°C），湿度85%，西南风2级",
        "source": "用户准备去上海",
        "event_date": "2025-07-25"
      }
    },
    "直属亲属地址": {
      "北京": {
        "weather_info": "当前时间2025-07-25，北京的天气为阵雨，温度28°C（体感温度33°C），湿度88%，南风1级",
        "source": "配偶李四居住地"
      }
    }
  },
  "ai_reminder": "张三，今天上海多云，体感温度30°C，湿度较高，比较闷热。你准备去上海，建议穿透气的衣服，随身带伞以防突然降雨。北京你的家乡正在下阵雨，体感温度33°C，如果李四和小明在北京，提醒他们出行注意防雨防滑。作为40岁的技术专家，注意劳逸结合，高温高湿天气容易疲劳，多补充水分。"
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "用户不存在或无权限访问"
}
```

```json
{
  "result": "error",
  "reason": "用户档案信息不完整",
  "suggestion": "请先完善个人档案信息，包括当前城市、家乡等"
}
```

**功能特点**:

- **智能地址提取**: 自动从用户档案、事件记录、亲属关系中提取相关地址
- **多维度天气**: 覆盖用户生活、工作、家庭、社交等多个维度的天气需求
- **个性化提醒**: 基于用户年龄、职业、家庭情况生成针对性的天气建议
- **事件关联**: 结合用户近期事件和行程安排提供相关天气信息

**使用场景**:

- 用户每日天气概览
- 出行前的综合天气检查
- 关心家人朋友的天气状况
- 重要事件前的天气准备

---

## 天气相关信息接口

### 1. 获取与天气相关的人物档案和事件

**接口**: `GET /humanrelation/get_weather_related_info`

**描述**: 获取与天气相关的人物档案和事件信息，用于天气播报。主要包括亲人的居住地信息和与出行、天气相关的事件。

**请求参数**:

- `user_id` (query parameter): 用户 ID

**响应示例**:

```json
{
  "result": "success",
  "user_id": "user123",
  "timestamp": "2025-01-23T18:00:00",
  "weather_related_persons": [
    {
      "person_id": "person-uuid-1",
      "canonical_name": "张父",
      "relationship": "父亲",
      "profile_summary": "我的父亲，退休教师，住在北京海淀区",
      "location_info": {
        "居住地": "北京",
        "当前城市": "北京海淀区"
      }
    },
    {
      "person_id": "person-uuid-2",
      "canonical_name": "李母",
      "relationship": "母亲",
      "profile_summary": "我的母亲，在上海工作生活",
      "location_info": {
        "工作地点": "上海",
        "居住地": "上海浦东"
      }
    }
  ],
  "weather_related_events": [
    {
      "event_id": "event-uuid-1",
      "description_text": "明天要去打球，约了朋友在体育馆见面",
      "location": "体育馆",
      "timestamp": "2025-01-22T10:00:00",
      "participants": ["user123", "friend-id"]
    },
    {
      "event_id": "event-uuid-2",
      "description_text": "下周要出差去深圳，2025-01-28出发",
      "location": "深圳",
      "timestamp": "2025-01-20T15:30:00",
      "participants": ["user123"]
    }
  ],
  "summary": {
    "total_persons": 2,
    "total_events": 2
  }
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "获取天气相关信息失败: 数据库连接失败"
}
```

**功能特点**:

- **智能筛选亲人**: 自动识别父母、配偶、子女等直系亲属关系
- **地址提取**: 从人物档案的多个字段中提取居住地、工作地点等信息
- **事件过滤**: 识别与出行、户外活动、天气相关的事件
- **未来事件**: 优先返回未来的计划和安排
- **数据精简**: 只返回必要的字段（profile_summary 和 description_text）

**支持的亲人关系类型**:

- 直系亲属：父亲、母亲、爸爸、妈妈、配偶、妻子、丈夫、老公、老婆、儿子、女儿
- 兄弟姐妹：兄弟、姐妹、哥哥、弟弟、姐姐、妹妹
- 祖辈：祖父、祖母、爷爷、奶奶、外公、外婆、岳父、岳母、公公、婆婆

**支持的天气相关事件类型**:

- 出行计划：出行、旅行、旅游、出差
- 户外活动：打球、运动、跑步、散步、爬山、徒步
- 社交活动：看电影、逛街、购物、聚餐、聚会
- 地点相关：有具体地点信息的事件
- 未来计划：包含"明天"、"后天"、"下周"等时间关键词的事件

**使用场景**:

- 天气播报前的信息收集
- 关心家人所在地的天气状况
- 提醒用户未来出行的天气准备
- 个性化天气建议生成

---

## 话题推荐接口

### 1. 根据人员档案和事件推荐话题

**接口**: `POST /humanrelation/recommend_topics`

**描述**: 基于指定人员档案和近期事件，智能生成个性化、生活化的聊天话题推荐

**请求参数**:

```json
{
  "user_id": "string", // 请求用户ID（必需，用于权限验证）
  "person_id": "string", // 目标人员ID（必需，为谁生成话题推荐）
  "context": "string", // 额外上下文信息（可选）
  "max_topics": 5, // 最大推荐话题数量，默认5个，范围1-5
  "fast_mode": false // 快速模式，跳过AI调用直接返回备用话题（可选）
}
```

**功能特点**:

- **智能个性化**: 基于指定 person 的档案和近期事件生成个性化话题
- **支持多人员**: 可为用户本人或其他任何 person 生成话题推荐
- **生活化有趣**: 生成贴近生活、有趣味性的话题
- **优先级排序**: AI 会为话题分配优先级，帮助用户选择最合适的话题
- **分类标签**: 每个话题都有分类标签，重点推荐美食、兴趣、生活话题（优先级 4-5 分）
- **上下文感知**: 考虑时间、季节和 person 背景等上下文因素
- **备用机制**: 当 AI 生成失败时，提供备用话题保证功能可用

**话题生成逻辑**:

1. **收集人员信息**: 获取指定 person 的档案、关键属性、人际关系等基础信息
2. **提取事件信息**: 获取 person 的近期事件、提醒事项和相关活动
3. **AI 智能分析**: 基于收集的信息，使用 AI 生成合适的话题建议
4. **优化排序**: 根据相关性、时效性和实用性对话题进行排序

**成功响应示例**:

```json
{
  "result": "success",
  "user_id": "test_user_001",
  "person_id": "person_123",
  "recommended_topics": [
    {
      "topic": "你平时都喜欢吃什么？",
      "category": "food",
      "description": "美食话题，人人都有话说",
      "priority": 4
    },
    {
      "topic": "最近有在学什么新技能吗？",
      "category": "hobby",
      "description": "兴趣学习话题，容易引起共鸣",
      "priority": 4
    },
    {
      "topic": "最近有什么新电影推荐？",
      "category": "entertainment",
      "description": "娱乐话题，适合轻松聊天",
      "priority": 3
    },
    {
      "topic": "最近有什么好玩的地方推荐？",
      "category": "travel",
      "description": "旅行话题，容易引起共鸣",
      "priority": 3
    },
    {
      "topic": "最近有什么有趣的事情吗？",
      "category": "life",
      "description": "生活话题，通用开场白",
      "priority": 4
    }
  ],
  "context_summary": {
    "user_profile_available": true,
    "events_count": 5,
    "has_reminders": true
  },
  "generated_at": "2024-01-01 12:00:00"
}
```

**错误响应示例**:

```json
{
  "result": "error",
  "reason": "person_id不能为空",
  "user_id": "test_user_001",
  "person_id": "",
  "recommended_topics": []
}
```

**使用场景**:

1. **多人社交**: 为不同的朋友、同事、家人生成针对性的聊天话题
2. **个性化推荐**: 基于每个人的兴趣爱好和生活背景提供定制化话题
3. **社交破冰**: 为与特定 person 聊天提供合适的开场话题
4. **朋友聚会**: 为聚会、饭局等场合提供轻松有趣的聊天话题
5. **关系维护**: 基于 person 的近期动态生成贴心的关怀话题

**话题分类说明**:

- `food`: 美食餐厅话题（优先级 4-5）
- `hobby`: 兴趣爱好话题（优先级 4-5）
- `life`: 生活日常话题（优先级 4-5）
- `entertainment`: 娱乐相关话题（电影、音乐、综艺等，优先级 3-4）
- `travel`: 出行旅游话题（优先级 3-4）
- `work`: 工作相关话题（优先级 3）
- `general`: 通用话题（优先级 2）

**注意事项**:

1. 话题生成需要消耗 AI 模型 token，建议合理设置`max_topics`参数
2. 功能依赖用户档案和事件数据，数据越完整推荐越准确
3. 系统会自动过滤过于私人或敏感的话题
4. 接口支持并发调用，但需要注意 AI 服务的 QPS 限制
5. 当 AI 生成失败时，会返回预设的备用话题确保功能可用

---

## 系统监控接口

### 1. 根路径检查

**接口**: `GET /`

**描述**: 基础根路径检查接口

**响应示例**:

```
"Hello World"
```

### 2. 健康检查

**接口**: `GET /monitor/alive`

**描述**: 健康检查接口，用于部署时的存活性检查

**响应示例**:

```json
{
  "status": "ok",
  "message": "Service is alive"
}
```

---

## 错误响应格式

所有接口在发生错误时都会返回统一的错误格式：

```json
{
  "status": "error",
  "message": "错误描述信息",
  "error_code": "ERROR_CODE"
}
```

## 认证说明

所有接口都需要提供 `user_id` 参数进行用户身份识别。系统使用用户 ID 来隔离不同用户的数据。

## 注意事项

1. 所有 POST 请求的 Content-Type 应为 `application/json`
2. 流式聊天接口返回的是 Server-Sent Events 格式，支持推荐问题功能
3. JSON 格式聊天接口支持推荐问题生成，可通过 Lion 配置中心控制开关
4. 人员管理相关接口支持 MySQL 数据库存储
5. 记忆管理使用 ElasticSearch 进行语义搜索
6. 系统支持长期和短期记忆的分类管理
7. 提醒系统支持后台线程自动处理，每 10 分钟检查一次待处理提醒
8. 提醒管理支持完整的 CRUD 操作：添加、删除、查询，确保用户数据隔离
9. 聊天记录通过 MySQL 检查点保存器进行持久化存储
10. 会话管理支持智能摘要生成，便于用户快速识别对话主题
11. 短期记忆（事件记忆）存储在 ElasticSearch 中，通过 timestamp 字段按时间排序
12. 获取人员 ID 列表接口默认限制返回 1000 条记录，避免数据量过大
13. 所有记忆相关接口都支持用户数据隔离，确保数据安全性
14. 事件管理接口支持完整的 CRUD 操作：添加、查询、更新、删除
15. 删除事件接口具有安全保护机制，用户只能删除自己的事件
16. 添加事件时，描述文本和参与者信息至少需要提供一个
17. 事件索引名称通过 Lion 配置中心统一管理，支持不同环境使用不同索引
18. 天气查询接口支持智能地点提取，可从人员档案的多个字段中识别地点信息
19. 天气接口支持多地点查询，当人员有多个相关地点时会返回所有地点的天气
20. 天气查询具有权限控制机制，用户只能查询自己有权限访问的人员档案
21. 天气 API 仅有生产端点，支持地理编码和天气数据获取
22. 用户本人档案会在首次使用时自动创建，`is_user` 字段标识为 `true`
23. 系统启动时会自动启动提醒检查线程、新闻爬虫线程和长期记忆更新线程
24. 推荐问题功能基于对话上下文智能生成，支持配置化控制和容错处理
25. 会话列表接口支持分页查询，默认每页 20 条，最大 100 条，有效控制数据传输量
26. 会话摘要采用异步生成机制，首次访问立即返回"生成中..."状态，后台异步生成完整摘要
27. 数据库查询已优化为批量查询，避免 N+1 查询问题，并建议创建相应索引提升性能
28. 接口响应包含详细的性能统计信息，便于监控和调试
29. 系统支持双向关系处理机制，自动维护家庭关系的完整性和一致性
30. 双向关系处理支持父女、母子、夫妻等关系类型，自动更新相关属性字段
31. 子女信息会自动添加到父母档案的 `key_attributes.基本信息.家庭情况.子女信息` 字段中
32. 配偶信息会自动更新到双方档案的 `key_attributes.基本信息.家庭情况.配偶姓名` 字段中
33. 双向关系处理具有去重机制，避免重复添加相同的关系记录
34. 关系处理失败不影响主要功能，系统具有良好的容错能力

## 性能优化说明

### 会话列表性能优化

为了应对用户会话数量不断增长的场景，系统对会话列表接口进行了全面的性能优化：

#### 1. 分页查询

- **问题**：随着用户使用时间增长，会话数量可能达到数百甚至上千个
- **解决方案**：实现分页查询，每次只返回指定数量的会话
- **效果**：无论用户有多少会话，响应时间都保持在 0.1-0.2 秒

#### 2. 数据库查询优化

- **问题**：原来的 N+1 查询问题，如果有 N 个会话需要执行 N+1 次数据库查询
- **解决方案**：使用批量查询，将多次查询合并为单次查询
- **效果**：查询次数从 43 次降到 2 次，减少 95%的数据库 IO

#### 3. 异步摘要生成

- **问题**：生成会话摘要需要调用 AI 服务，可能耗时几秒
- **解决方案**：异步生成摘要，用户立即看到"生成中..."，后台完成后自动更新
- **效果**：用户体验从等待几秒变为立即响应

#### 4. 数据库索引建议

为了进一步提升性能，建议创建以下索引：

```sql
-- 用户和会话ID复合索引，优化摘要查询
CREATE INDEX idx_user_thread ON chat_history(user_id, thread_id);

-- 用户ID索引，优化会话列表查询
CREATE INDEX idx_user_id ON chat_history(user_id);
```

#### 5. 性能监控

所有接口都包含性能统计信息：

- `total_time_seconds`: 接口总耗时
- `db_query_time_seconds`: 数据库查询耗时
- `conversation_count`: 返回的会话数量

### 性能表现对比

| 场景               | 优化前   | 优化后   | 提升效果   |
| ------------------ | -------- | -------- | ---------- |
| 常规使用（有缓存） | 1.953 秒 | 0.170 秒 | 提升 91.3% |
| 分页查询           | N/A      | 0.132 秒 | 新功能     |
| 数据库查询次数     | 43 次    | 2 次     | 减少 95%   |
| 内存占用           | 全量加载 | 按需加载 | 大幅减少   |

这些优化确保了系统在用户数据量增长的情况下仍能保持良好的性能表现。
