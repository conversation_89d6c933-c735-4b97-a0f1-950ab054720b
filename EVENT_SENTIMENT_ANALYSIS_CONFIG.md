# 事件情感分析功能 Lion 配置项

## 概述
事件情感分析功能为添加人员事件接口提供AI自动情感判断能力，当用户未手动指定事件情感时，系统会自动分析事件描述文本的情感倾向。

## 配置项

### 1. 事件情感分析提示词
**配置键**: `humanrelation.event_sentiment_analysis_prompt`
**说明**: 用于AI分析事件情感倾向的提示词
**默认值**: 见下方默认提示词

**默认提示词内容**:
```
你是一个情感分析专家。请分析给定的事件描述文本，判断其情感倾向。

分析标准：
- positive（积极）：表达正面情绪的事件，如成功、快乐、庆祝、成就、友好交流等
- negative（消极）：表达负面情绪的事件，如失败、悲伤、冲突、问题、不愉快等  
- neutral（中性）：客观描述的事件，没有明显的情感色彩，如日常活动、会议、信息记录等

请只返回以下三个值之一：positive、negative、neutral
```

## 功能特点

### 1. 自动触发机制
- 当调用 `POST /humanrelation/add_person_event` 接口时
- 如果请求中未提供 `sentiment` 字段或该字段为空
- 且提供了 `description_text` 事件描述
- 系统会自动调用AI分析情感倾向

### 2. 情感分类标准
- **positive（积极）**: 成功、快乐、庆祝、成就、友好交流、好消息等
- **negative（消极）**: 失败、悲伤、冲突、问题、不愉快、坏消息等
- **neutral（中性）**: 日常活动、会议、信息记录、客观描述等

### 3. 容错处理
- AI服务调用失败时，默认返回 "neutral"
- AI返回无效结果时，默认返回 "neutral"
- 确保接口稳定性，不因情感分析失败而影响事件添加

### 4. 性能优化
- 使用较低的温度值（0.1）确保结果稳定
- 限制最大token数（10）提高响应速度
- 关闭详细日志记录减少性能开销

## 使用示例

### 自动情感分析示例
```json
// 请求（未提供sentiment）
{
  "user_id": "user123",
  "description_text": "今天和张三一起完成了项目，大家都很开心",
  "participants": ["张三"]
}

// 系统会自动分析为 "positive"
```

### 手动指定情感示例
```json
// 请求（手动指定sentiment）
{
  "user_id": "user123", 
  "description_text": "参加了部门会议",
  "participants": ["李四", "王五"],
  "sentiment": "neutral"
}

// 系统会使用手动指定的 "neutral"，不进行AI分析
```

## 配置建议

### 1. 提示词优化
可以根据业务需求调整提示词，例如：
- 增加特定领域的情感判断标准
- 调整情感分类的细粒度
- 添加特定场景的示例

### 2. 模型选择
情感分析使用与记忆提取相同的模型配置：
- 配置键: `humanrelation.memory_extraction_model`
- 默认值: "gpt-4o-mini"
- 建议使用轻量级模型以提高响应速度

## 监控和日志

### 1. 日志记录
- 成功分析: `INFO` 级别记录分析结果
- 分析失败: `ERROR` 级别记录错误信息
- 无效结果: `WARNING` 级别记录并使用默认值

### 2. 监控指标
建议监控以下指标：
- 情感分析调用次数
- 分析成功率
- 各情感类型的分布
- 分析响应时间

## 注意事项

1. **隐私保护**: 事件描述文本会发送给AI服务进行分析，请确保符合隐私政策
2. **成本控制**: 每次自动分析都会产生AI调用成本，可考虑添加缓存机制
3. **准确性**: AI分析结果可能不完全准确，重要场景建议手动指定情感
4. **语言支持**: 当前主要针对中文优化，其他语言可能需要调整提示词
